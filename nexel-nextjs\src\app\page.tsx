import HeroSection from "@/components/sections/HeroSection";
import FeaturesSection from "@/components/sections/FeaturesSection";
import PortfolioPreviewSection from "@/components/sections/PortfolioPreviewSection";
import CTASection from "@/components/sections/CTASection";

export default function Home() {
  return (
    <>
      <HeroSection
        title="AI Engineer for PCB Design"
        subtitle="Nexel is the AI-powered platform that transforms your product ideas into ready-to-build PCBs. Design, simulate, and prototype hardware—faster than ever."
        ctaText="Start Free Trial"
        ctaLink="/contact"
        secondaryCtaText="View Demo"
        secondaryCtaLink="/portfolio"
      />
      <FeaturesSection />
      <PortfolioPreviewSection />
      <CTASection
        title="Ready to Transform Your Hardware Development?"
        description="Join innovative companies already using Nexel to accelerate their PCB design process. Start your free trial today and experience the future of hardware development."
        buttonText="Start Free Trial"
        buttonLink="/contact"
        secondaryButtonText="Schedule Demo"
        secondaryButtonLink="/contact?demo=true"
        background="primary"
      />
    </>
  );
}
