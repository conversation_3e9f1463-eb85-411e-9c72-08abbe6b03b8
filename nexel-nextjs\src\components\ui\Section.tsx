import { cn } from "@/lib/design-system";
import type { SectionProps } from "@/types";

export default function Section({ 
  children, 
  background = "white", 
  padding = "lg", 
  className 
}: SectionProps) {
  const backgrounds = {
    white: "bg-white",
    gray: "bg-gray-light",
    primary: "bg-primary",
    dark: "bg-dark",
  };

  const paddings = {
    sm: "py-8",
    md: "py-12",
    lg: "py-16 lg:py-24",
    xl: "py-20 lg:py-32",
  };

  return (
    <section className={cn(backgrounds[background], paddings[padding], className)}>
      {children}
    </section>
  );
}
