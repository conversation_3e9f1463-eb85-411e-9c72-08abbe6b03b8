{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Zero%20Engineer/nexel-nextjs/src/components/ui/Container.tsx"], "sourcesContent": ["import { cn } from \"@/lib/design-system\";\nimport type { ContainerProps } from \"@/types\";\n\nexport default function Container({ children, size = \"lg\", className }: ContainerProps) {\n  const sizes = {\n    sm: \"max-w-3xl\",\n    md: \"max-w-5xl\",\n    lg: \"max-w-7xl\",\n    xl: \"max-w-screen-2xl\",\n  };\n\n  return (\n    <div className={cn(\"mx-auto px-4 sm:px-6 lg:px-8\", sizes[size], className)}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAGe,SAAS,UAAU,EAAE,QAAQ,EAAE,OAAO,IAAI,EAAE,SAAS,EAAkB;IACpF,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,8HAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC,KAAK,CAAC,KAAK,EAAE;kBAC7D;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Zero%20Engineer/nexel-nextjs/src/components/ui/Button.tsx"], "sourcesContent": ["import { forwardRef } from \"react\";\nimport { cn } from \"@/lib/design-system\";\nimport type { ButtonProps } from \"@/types\";\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ variant = \"primary\", size = \"md\", children, className, ...props }, ref) => {\n    const baseStyles = \"inline-flex items-center justify-center rounded-md font-medium transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\";\n    \n    const variants = {\n      primary: \"bg-primary text-white hover:bg-primary-600 active:bg-primary-700\",\n      secondary: \"bg-white text-primary border border-primary hover:bg-primary-50 active:bg-primary-100\",\n      ghost: \"text-primary hover:bg-primary-50 active:bg-primary-100\",\n      outline: \"border border-gray-20 text-dark hover:bg-gray-light active:bg-gray-10\",\n    };\n\n    const sizes = {\n      sm: \"px-3 py-1.5 text-sm\",\n      md: \"px-4 py-2 text-sm\",\n      lg: \"px-6 py-3 text-base\",\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAGA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACpE,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,OAAO;QACP,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,8HAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Zero%20Engineer/nexel-nextjs/src/components/sections/HeroSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport Link from \"next/link\";\nimport Container from \"@/components/ui/Container\";\nimport Button from \"@/components/ui/Button\";\nimport { cn } from \"@/lib/design-system\";\nimport type { HeroSectionProps } from \"@/types\";\n\nexport default function HeroSection({\n  title,\n  subtitle,\n  ctaText = \"Get Started\",\n  ctaLink = \"/contact\",\n  secondaryCtaText = \"Learn More\",\n  secondaryCtaLink = \"/about\",\n  className,\n}: HeroSectionProps) {\n  return (\n    <section className={cn(\"relative overflow-hidden bg-gradient-to-br from-white via-primary-50 to-primary-100\", className)}>\n      <Container className=\"relative z-10\">\n        <div className=\"flex flex-col items-center text-center py-20 lg:py-32\">\n          {/* Main Heading */}\n          <motion.h1\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-display font-bold text-dark mb-6 max-w-4xl\"\n          >\n            {title}\n          </motion.h1>\n\n          {/* Subtitle */}\n          {subtitle && (\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              className=\"text-lg md:text-xl lg:text-2xl text-gray mb-8 max-w-3xl leading-relaxed\"\n            >\n              {subtitle}\n            </motion.p>\n          )}\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"flex flex-col sm:flex-row gap-4 mb-12\"\n          >\n            <Link href={ctaLink}>\n              <Button size=\"lg\" className=\"w-full sm:w-auto\">\n                {ctaText}\n              </Button>\n            </Link>\n            <Link href={secondaryCtaLink}>\n              <Button variant=\"ghost\" size=\"lg\" className=\"w-full sm:w-auto\">\n                {secondaryCtaText}\n              </Button>\n            </Link>\n          </motion.div>\n\n          {/* Hero Visual */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"relative w-full max-w-4xl\"\n          >\n            <div className=\"relative bg-white rounded-2xl shadow-2xl p-8 border border-gray-10\">\n              {/* Placeholder for hero image/demo */}\n              <div className=\"aspect-video bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <span className=\"text-white font-bold text-2xl\">N</span>\n                  </div>\n                  <p className=\"text-primary font-medium\">AI-Powered PCB Design Platform</p>\n                </div>\n              </div>\n            </div>\n            \n            {/* Floating elements */}\n            <motion.div\n              animate={{ y: [0, -10, 0] }}\n              transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n              className=\"absolute -top-4 -left-4 w-8 h-8 bg-primary rounded-full opacity-20\"\n            />\n            <motion.div\n              animate={{ y: [0, 10, 0] }}\n              transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\", delay: 1 }}\n              className=\"absolute -bottom-4 -right-4 w-12 h-12 bg-accent-blue rounded-full opacity-20\"\n            />\n            <motion.div\n              animate={{ y: [0, -15, 0] }}\n              transition={{ duration: 5, repeat: Infinity, ease: \"easeInOut\", delay: 2 }}\n              className=\"absolute top-1/2 -right-8 w-6 h-6 bg-accent-red rounded-full opacity-20\"\n            />\n          </motion.div>\n\n          {/* Trust indicators */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.6, delay: 1.2 }}\n            className=\"mt-16 text-center\"\n          >\n            <p className=\"text-sm text-gray mb-4\">Trusted by innovative companies</p>\n            <div className=\"flex flex-wrap justify-center items-center gap-8 opacity-60\">\n              {/* Placeholder for company logos */}\n              {[1, 2, 3, 4].map((i) => (\n                <div key={i} className=\"w-24 h-8 bg-gray-20 rounded\"></div>\n              ))}\n            </div>\n          </motion.div>\n        </div>\n      </Container>\n\n      {/* Background decoration */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-primary opacity-5 rounded-full blur-3xl\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-accent-blue opacity-5 rounded-full blur-3xl\"></div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AACA;AACA;AACA;AANA;;;;;;;AASe,SAAS,YAAY,EAClC,KAAK,EACL,QAAQ,EACR,UAAU,aAAa,EACvB,UAAU,UAAU,EACpB,mBAAmB,YAAY,EAC/B,mBAAmB,QAAQ,EAC3B,SAAS,EACQ;IACjB,qBACE,8OAAC;QAAQ,WAAW,CAAA,GAAA,8HAAA,CAAA,KAAE,AAAD,EAAE,uFAAuF;;0BAC5G,8OAAC,qIAAA,CAAA,UAAS;gBAAC,WAAU;0BACnB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,OAAO,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAET;;;;;;wBAIF,0BACC,8OAAC,OAAO,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAET;;;;;;sCAKL,8OAAC,OAAO,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM;8CACV,cAAA,8OAAC,kIAAA,CAAA,UAAM;wCAAC,MAAK;wCAAK,WAAU;kDACzB;;;;;;;;;;;8CAGL,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM;8CACV,cAAA,8OAAC,kIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;kDACzC;;;;;;;;;;;;;;;;;sCAMP,8OAAC,OAAO,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAK;4BACnC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAgC;;;;;;;;;;;8DAElD,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;;;;;;;;;;;;;;;;;8CAM9C,8OAAC,OAAO,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC;4CAAG,CAAC;4CAAI;yCAAE;oCAAC;oCAC1B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAY;oCAC/D,WAAU;;;;;;8CAEZ,8OAAC,OAAO,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC;4CAAG;4CAAI;yCAAE;oCAAC;oCACzB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;wCAAa,OAAO;oCAAE;oCACzE,WAAU;;;;;;8CAEZ,8OAAC,OAAO,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC;4CAAG,CAAC;4CAAI;yCAAE;oCAAC;oCAC1B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;wCAAa,OAAO;oCAAE;oCACzE,WAAU;;;;;;;;;;;;sCAKd,8OAAC,OAAO,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;8CAAyB;;;;;;8CACtC,8OAAC;oCAAI,WAAU;8CAEZ;wCAAC;wCAAG;wCAAG;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,8OAAC;4CAAY,WAAU;2CAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Zero%20Engineer/nexel-nextjs/src/components/ui/Section.tsx"], "sourcesContent": ["import { cn } from \"@/lib/design-system\";\nimport type { SectionProps } from \"@/types\";\n\nexport default function Section({ \n  children, \n  background = \"white\", \n  padding = \"lg\", \n  className \n}: SectionProps) {\n  const backgrounds = {\n    white: \"bg-white\",\n    gray: \"bg-gray-light\",\n    primary: \"bg-primary\",\n    dark: \"bg-dark\",\n  };\n\n  const paddings = {\n    sm: \"py-8\",\n    md: \"py-12\",\n    lg: \"py-16 lg:py-24\",\n    xl: \"py-20 lg:py-32\",\n  };\n\n  return (\n    <section className={cn(backgrounds[background], paddings[padding], className)}>\n      {children}\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAGe,SAAS,QAAQ,EAC9B,QAAQ,EACR,aAAa,OAAO,EACpB,UAAU,IAAI,EACd,SAAS,EACI;IACb,MAAM,cAAc;QAClB,OAAO;QACP,MAAM;QACN,SAAS;QACT,MAAM;IACR;IAEA,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAQ,WAAW,CAAA,GAAA,8HAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,QAAQ,EAAE;kBAChE;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Zero%20Engineer/nexel-nextjs/src/components/sections/FeaturesSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Zap, Brain, Cpu, Layers } from \"lucide-react\";\nimport Container from \"@/components/ui/Container\";\nimport Section from \"@/components/ui/Section\";\nimport { cn } from \"@/lib/design-system\";\nimport type { FeaturesSectionProps, Feature } from \"@/types\";\n\nconst defaultFeatures: Feature[] = [\n  {\n    id: \"ai-design\",\n    title: \"AI-Powered Design\",\n    description: \"Transform your ideas into professional PCB layouts using advanced AI algorithms that understand electronics design principles.\",\n    icon: \"brain\",\n  },\n  {\n    id: \"real-time-simulation\",\n    title: \"Real-time Simulation\",\n    description: \"Test and validate your designs instantly with our built-in simulation engine before moving to physical prototyping.\",\n    icon: \"zap\",\n  },\n  {\n    id: \"component-library\",\n    title: \"Extensive Component Library\",\n    description: \"Access thousands of verified components with real-time availability and pricing from trusted suppliers.\",\n    icon: \"cpu\",\n  },\n  {\n    id: \"collaborative-design\",\n    title: \"Collaborative Design\",\n    description: \"Work seamlessly with your team using real-time collaboration tools and version control for hardware projects.\",\n    icon: \"layers\",\n  },\n];\n\nconst iconMap = {\n  brain: Brain,\n  zap: Zap,\n  cpu: Cpu,\n  layers: Layers,\n};\n\nexport default function FeaturesSection({\n  features = defaultFeatures,\n  title = \"Revolutionize Your PCB Design Process\",\n  subtitle = \"Nexel combines the power of AI with intuitive design tools to make hardware development faster, smarter, and more accessible than ever before.\",\n  layout = \"grid\",\n  className,\n}: FeaturesSectionProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\",\n      },\n    },\n  };\n\n  return (\n    <Section background=\"white\" className={className}>\n      <Container>\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-display font-bold text-dark mb-6\">\n            {title}\n          </h2>\n          {subtitle && (\n            <p className=\"text-lg md:text-xl text-gray max-w-3xl mx-auto leading-relaxed\">\n              {subtitle}\n            </p>\n          )}\n        </motion.div>\n\n        {/* Features Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className={cn(\n            layout === \"grid\"\n              ? \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\"\n              : \"space-y-8\"\n          )}\n        >\n          {features.map((feature, index) => {\n            const IconComponent = iconMap[feature.icon as keyof typeof iconMap] || Brain;\n            \n            return (\n              <motion.div\n                key={feature.id}\n                variants={itemVariants}\n                className=\"group\"\n              >\n                <div className=\"relative p-6 bg-white rounded-2xl border border-gray-10 hover:border-primary-200 transition-all duration-300 hover:shadow-lg\">\n                  {/* Icon */}\n                  <div className=\"w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mb-4 group-hover:bg-primary-200 transition-colors duration-300\">\n                    <IconComponent className=\"w-6 h-6 text-primary\" />\n                  </div>\n\n                  {/* Content */}\n                  <h3 className=\"text-xl font-display font-semibold text-dark mb-3\">\n                    {feature.title}\n                  </h3>\n                  <p className=\"text-gray leading-relaxed\">\n                    {feature.description}\n                  </p>\n\n                  {/* Hover effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-primary-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl pointer-events-none\" />\n                </div>\n              </motion.div>\n            );\n          })}\n        </motion.div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"text-center mt-16\"\n        >\n          <p className=\"text-gray mb-6\">\n            Ready to experience the future of PCB design?\n          </p>\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"bg-primary text-white px-8 py-3 rounded-lg font-medium hover:bg-primary-600 transition-colors duration-200\"\n          >\n            Start Your Free Trial\n          </motion.button>\n        </motion.div>\n      </Container>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAIA;AACA;AACA;AANA;;;;;;;AASA,MAAM,kBAA6B;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAED,MAAM,UAAU;IACd,OAAO;IACP,KAAK;IACL,KAAK;IACL,QAAQ;AACV;AAEe,SAAS,gBAAgB,EACtC,WAAW,eAAe,EAC1B,QAAQ,uCAAuC,EAC/C,WAAW,gJAAgJ,EAC3J,SAAS,MAAM,EACf,SAAS,EACY;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,YAAW;QAAQ,WAAW;kBACrC,cAAA,8OAAC,qIAAA,CAAA,UAAS;;8BAER,8OAAC,OAAO,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCACX;;;;;;wBAEF,0BACC,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAMP,8OAAC,OAAO,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAW,CAAA,GAAA,8HAAA,CAAA,KAAE,AAAD,EACV,WAAW,SACP,yDACA;8BAGL,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,gBAAgB,OAAO,CAAC,QAAQ,IAAI,CAAyB,IAAI;wBAEvE,qBACE,8OAAC,OAAO,GAAG;4BAET,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAc,WAAU;;;;;;;;;;;kDAI3B,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,8OAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAItB,8OAAC;wCAAI,WAAU;;;;;;;;;;;;2BAnBZ,QAAQ,EAAE;;;;;oBAuBrB;;;;;;8BAIF,8OAAC,OAAO,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAE,WAAU;sCAAiB;;;;;;sCAG9B,8OAAC,OAAO,MAAM;4BACZ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Zero%20Engineer/nexel-nextjs/src/components/sections/PortfolioPreviewSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport Container from \"@/components/ui/Container\";\nimport Section from \"@/components/ui/Section\";\nimport Button from \"@/components/ui/Button\";\nimport { cn } from \"@/lib/design-system\";\nimport type { Project } from \"@/types\";\n\nconst featuredProjects: Project[] = [\n  {\n    id: \"lemonide-tech\",\n    title: \"Lemonide Tech\",\n    description: \"AI-powered IoT sensor platform for smart agriculture with real-time monitoring and predictive analytics.\",\n    image: \"/portfolio/lemonide-tech.jpg\",\n    slug: \"lemonide-tech\",\n    category: \"IoT & Agriculture\",\n    featured: true,\n    technologies: [\"AI/ML\", \"IoT\", \"PCB Design\"],\n    client: \"Lemonide Technologies\",\n    year: 2024,\n  },\n  {\n    id: \"million-one\",\n    title: \"Million One\",\n    description: \"High-performance computing board for edge AI applications with optimized power efficiency.\",\n    image: \"/portfolio/million-one.jpg\",\n    slug: \"million-one\",\n    category: \"Edge Computing\",\n    featured: true,\n    technologies: [\"Edge AI\", \"High Performance\", \"Power Optimization\"],\n    client: \"Million One Systems\",\n    year: 2024,\n  },\n  {\n    id: \"viper-studio\",\n    title: \"Viper Studio\",\n    description: \"Professional audio processing unit with custom DSP implementation and ultra-low latency design.\",\n    image: \"/portfolio/viper-studio.jpg\",\n    slug: \"viper-studio\",\n    category: \"Audio Technology\",\n    featured: true,\n    technologies: [\"DSP\", \"Audio Processing\", \"Low Latency\"],\n    client: \"Viper Studio\",\n    year: 2023,\n  },\n];\n\ninterface PortfolioPreviewSectionProps {\n  title?: string;\n  subtitle?: string;\n  projects?: Project[];\n  className?: string;\n}\n\nexport default function PortfolioPreviewSection({\n  title = \"Transforming Ideas into Reality\",\n  subtitle = \"See how we've helped innovative companies bring their hardware visions to life with our AI-powered PCB design platform.\",\n  projects = featuredProjects,\n  className,\n}: PortfolioPreviewSectionProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\",\n      },\n    },\n  };\n\n  return (\n    <Section background=\"gray\" className={className}>\n      <Container>\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-display font-bold text-dark mb-6\">\n            {title}\n          </h2>\n          {subtitle && (\n            <p className=\"text-lg md:text-xl text-gray max-w-3xl mx-auto leading-relaxed\">\n              {subtitle}\n            </p>\n          )}\n        </motion.div>\n\n        {/* Projects Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12\"\n        >\n          {projects.slice(0, 3).map((project, index) => (\n            <motion.div\n              key={project.id}\n              variants={itemVariants}\n              className=\"group\"\n            >\n              <Link href={`/portfolio/${project.slug}`}>\n                <div className=\"bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300 group-hover:-translate-y-2\">\n                  {/* Project Image */}\n                  <div className=\"relative aspect-video bg-gradient-to-br from-primary-100 to-primary-200 overflow-hidden\">\n                    {/* Placeholder for project image */}\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\n                      <div className=\"text-center\">\n                        <div className=\"w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4\">\n                          <span className=\"text-white font-bold text-xl\">\n                            {project.title.charAt(0)}\n                          </span>\n                        </div>\n                        <p className=\"text-primary font-medium\">{project.category}</p>\n                      </div>\n                    </div>\n                    \n                    {/* Overlay */}\n                    <div className=\"absolute inset-0 bg-dark-30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\n                      <span className=\"text-white font-medium\">View Case Study</span>\n                    </div>\n                  </div>\n\n                  {/* Project Content */}\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <span className=\"text-sm font-medium text-primary bg-primary-100 px-3 py-1 rounded-full\">\n                        {project.category}\n                      </span>\n                      <span className=\"text-sm text-gray\">{project.year}</span>\n                    </div>\n                    \n                    <h3 className=\"text-xl font-display font-semibold text-dark mb-3 group-hover:text-primary transition-colors duration-200\">\n                      {project.title}\n                    </h3>\n                    \n                    <p className=\"text-gray leading-relaxed mb-4\">\n                      {project.description}\n                    </p>\n\n                    {/* Technologies */}\n                    {project.technologies && (\n                      <div className=\"flex flex-wrap gap-2\">\n                        {project.technologies.slice(0, 3).map((tech) => (\n                          <span\n                            key={tech}\n                            className=\"text-xs text-gray bg-gray-10 px-2 py-1 rounded\"\n                          >\n                            {tech}\n                          </span>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </Link>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* View All CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"text-center\"\n        >\n          <Link href=\"/portfolio\">\n            <Button variant=\"outline\" size=\"lg\">\n              View All Projects\n            </Button>\n          </Link>\n        </motion.div>\n      </Container>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AAEA;AACA;AACA;AAPA;;;;;;;AAWA,MAAM,mBAA8B;IAClC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,cAAc;YAAC;YAAS;YAAO;SAAa;QAC5C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,cAAc;YAAC;YAAW;YAAoB;SAAqB;QACnE,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,cAAc;YAAC;YAAO;YAAoB;SAAc;QACxD,QAAQ;QACR,MAAM;IACR;CACD;AASc,SAAS,wBAAwB,EAC9C,QAAQ,iCAAiC,EACzC,WAAW,yHAAyH,EACpI,WAAW,gBAAgB,EAC3B,SAAS,EACoB;IAC7B,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,YAAW;QAAO,WAAW;kBACpC,cAAA,8OAAC,qIAAA,CAAA,UAAS;;8BAER,8OAAC,OAAO,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCACX;;;;;;wBAEF,0BACC,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAMP,8OAAC,OAAO,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAClC,8OAAC,OAAO,GAAG;4BAET,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,IAAI,EAAE;0CACtC,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EACb,QAAQ,KAAK,CAAC,MAAM,CAAC;;;;;;;;;;;0EAG1B,8OAAC;gEAAE,WAAU;0EAA4B,QAAQ,QAAQ;;;;;;;;;;;;;;;;;8DAK7D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;;;;;;sDAK7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,QAAQ,QAAQ;;;;;;sEAEnB,8OAAC;4DAAK,WAAU;sEAAqB,QAAQ,IAAI;;;;;;;;;;;;8DAGnD,8OAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAGhB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;gDAIrB,QAAQ,YAAY,kBACnB,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAhDd,QAAQ,EAAE;;;;;;;;;;8BAgErB,8OAAC,OAAO,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;4BAAC,SAAQ;4BAAU,MAAK;sCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Zero%20Engineer/nexel-nextjs/src/components/sections/CTASection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport Link from \"next/link\";\nimport Container from \"@/components/ui/Container\";\nimport Section from \"@/components/ui/Section\";\nimport Button from \"@/components/ui/Button\";\nimport { cn } from \"@/lib/design-system\";\nimport type { CTASectionProps } from \"@/types\";\n\nexport default function CTASection({\n  title,\n  description,\n  buttonText,\n  buttonLink,\n  secondaryButtonText,\n  secondaryButtonLink,\n  background = \"primary\",\n  className,\n}: CTASectionProps) {\n  const isLightBackground = background === \"white\" || background === \"gray\";\n  const textColor = isLightBackground ? \"text-dark\" : \"text-white\";\n  const subtextColor = isLightBackground ? \"text-gray\" : \"text-white-70\";\n\n  return (\n    <Section background={background} className={className}>\n      <Container>\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center max-w-4xl mx-auto\"\n        >\n          {/* Title */}\n          <h2 className={cn(\n            \"text-3xl md:text-4xl lg:text-5xl font-display font-bold mb-6\",\n            textColor\n          )}>\n            {title}\n          </h2>\n\n          {/* Description */}\n          {description && (\n            <p className={cn(\n              \"text-lg md:text-xl leading-relaxed mb-8\",\n              subtextColor\n            )}>\n              {description}\n            </p>\n          )}\n\n          {/* Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n          >\n            <Link href={buttonLink}>\n              <Button\n                variant={isLightBackground ? \"primary\" : \"secondary\"}\n                size=\"lg\"\n                className=\"w-full sm:w-auto\"\n              >\n                {buttonText}\n              </Button>\n            </Link>\n            \n            {secondaryButtonText && secondaryButtonLink && (\n              <Link href={secondaryButtonLink}>\n                <Button\n                  variant=\"ghost\"\n                  size=\"lg\"\n                  className={cn(\n                    \"w-full sm:w-auto\",\n                    isLightBackground \n                      ? \"text-primary hover:bg-primary-50\" \n                      : \"text-white hover:bg-white-10\"\n                  )}\n                >\n                  {secondaryButtonText}\n                </Button>\n              </Link>\n            )}\n          </motion.div>\n\n          {/* Decorative elements for primary background */}\n          {background === \"primary\" && (\n            <div className=\"relative mt-16\">\n              <motion.div\n                animate={{ rotate: 360 }}\n                transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute -top-8 -left-8 w-16 h-16 border-2 border-white-20 rounded-full\"\n              />\n              <motion.div\n                animate={{ rotate: -360 }}\n                transition={{ duration: 25, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute -bottom-8 -right-8 w-12 h-12 border-2 border-white-15 rounded-full\"\n              />\n              <motion.div\n                animate={{ y: [0, -10, 0] }}\n                transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute top-1/2 -right-12 w-6 h-6 bg-white-10 rounded-full\"\n              />\n            </div>\n          )}\n        </motion.div>\n      </Container>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAUe,SAAS,WAAW,EACjC,KAAK,EACL,WAAW,EACX,UAAU,EACV,UAAU,EACV,mBAAmB,EACnB,mBAAmB,EACnB,aAAa,SAAS,EACtB,SAAS,EACO;IAChB,MAAM,oBAAoB,eAAe,WAAW,eAAe;IACnE,MAAM,YAAY,oBAAoB,cAAc;IACpD,MAAM,eAAe,oBAAoB,cAAc;IAEvD,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,YAAY;QAAY,WAAW;kBAC1C,cAAA,8OAAC,qIAAA,CAAA,UAAS;sBACR,cAAA,8OAAC,OAAO,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAGV,8OAAC;wBAAG,WAAW,CAAA,GAAA,8HAAA,CAAA,KAAE,AAAD,EACd,gEACA;kCAEC;;;;;;oBAIF,6BACC,8OAAC;wBAAE,WAAW,CAAA,GAAA,8HAAA,CAAA,KAAE,AAAD,EACb,2CACA;kCAEC;;;;;;kCAKL,8OAAC,OAAO,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM;0CACV,cAAA,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAS,oBAAoB,YAAY;oCACzC,MAAK;oCACL,WAAU;8CAET;;;;;;;;;;;4BAIJ,uBAAuB,qCACtB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM;0CACV,cAAA,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAW,CAAA,GAAA,8HAAA,CAAA,KAAE,AAAD,EACV,oBACA,oBACI,qCACA;8CAGL;;;;;;;;;;;;;;;;;oBAOR,eAAe,2BACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,OAAO,GAAG;gCACT,SAAS;oCAAE,QAAQ;gCAAI;gCACvB,YAAY;oCAAE,UAAU;oCAAI,QAAQ;oCAAU,MAAM;gCAAS;gCAC7D,WAAU;;;;;;0CAEZ,8OAAC,OAAO,GAAG;gCACT,SAAS;oCAAE,QAAQ,CAAC;gCAAI;gCACxB,YAAY;oCAAE,UAAU;oCAAI,QAAQ;oCAAU,MAAM;gCAAS;gCAC7D,WAAU;;;;;;0CAEZ,8OAAC,OAAO,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;gCAAC;gCAC1B,YAAY;oCAAE,UAAU;oCAAG,QAAQ;oCAAU,MAAM;gCAAY;gCAC/D,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B", "debugId": null}}]}