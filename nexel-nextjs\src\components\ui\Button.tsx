import { forwardRef } from "react";
import { cn } from "@/lib/design-system";
import type { ButtonProps } from "@/types";

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = "primary", size = "md", children, className, ...props }, ref) => {
    const baseStyles = "inline-flex items-center justify-center rounded-md font-medium transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none";
    
    const variants = {
      primary: "bg-primary text-white hover:bg-primary-600 active:bg-primary-700",
      secondary: "bg-white text-primary border border-primary hover:bg-primary-50 active:bg-primary-100",
      ghost: "text-primary hover:bg-primary-50 active:bg-primary-100",
      outline: "border border-gray-20 text-dark hover:bg-gray-light active:bg-gray-10",
    };

    const sizes = {
      sm: "px-3 py-1.5 text-sm",
      md: "px-4 py-2 text-sm",
      lg: "px-6 py-3 text-base",
    };

    return (
      <button
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        {...props}
      >
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
