import { clsx, type ClassValue } from "clsx";

/**
 * Utility function to merge class names
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * Design system color palette
 */
export const colors = {
  primary: {
    DEFAULT: "rgb(79, 26, 214)",
    50: "rgba(79, 26, 214, 0.02)",
    100: "rgba(79, 26, 214, 0.05)",
    200: "rgba(79, 26, 214, 0.08)",
    300: "rgba(79, 26, 214, 0.15)",
    400: "rgba(79, 26, 214, 0.2)",
    500: "rgb(79, 26, 214)",
    600: "rgb(63, 21, 171)",
    700: "rgb(47, 16, 128)",
    800: "rgb(32, 11, 86)",
    900: "rgb(16, 5, 43)",
  },
  white: {
    DEFAULT: "rgb(255, 255, 255)",
    90: "rgba(255, 255, 255, 0.9)",
    80: "rgba(255, 255, 255, 0.8)",
    70: "rgba(255, 255, 255, 0.7)",
    60: "rgba(255, 255, 255, 0.6)",
    50: "rgba(255, 255, 255, 0.5)",
    30: "rgba(255, 255, 255, 0.3)",
    20: "rgba(255, 255, 255, 0.2)",
    15: "rgba(255, 255, 255, 0.15)",
    10: "rgba(255, 255, 255, 0.1)",
    8: "rgba(255, 255, 255, 0.08)",
    7: "rgba(255, 255, 255, 0.07)",
    5: "rgba(255, 255, 255, 0.05)",
    2: "rgba(255, 255, 255, 0.02)",
  },
  dark: {
    DEFAULT: "rgb(0, 0, 0)",
    light: "rgb(8, 8, 8)",
    medium: "rgb(19, 19, 19)",
    30: "rgba(0, 0, 0, 0.3)",
  },
  gray: {
    DEFAULT: "rgb(153, 153, 153)",
    light: "rgb(239, 238, 236)",
    10: "rgba(153, 153, 153, 0.1)",
    20: "rgba(51, 51, 51, 0.2)",
    90: "rgba(97, 97, 97, 0.09)",
  },
  accent: {
    red: "rgb(255, 0, 0)",
    blue: "rgb(0, 128, 255)",
  },
} as const;

/**
 * Typography scale
 */
export const typography = {
  fontFamily: {
    sans: ["DM Sans", "Inter", "Inter Placeholder", "sans-serif"],
    display: ["DM Sans", "sans-serif"],
    body: ["Inter", "Inter Placeholder", "sans-serif"],
  },
  fontSize: {
    xs: "0.75rem",
    sm: "0.875rem",
    base: "1rem",
    lg: "1.125rem",
    xl: "1.25rem",
    "2xl": "1.5rem",
    "3xl": "1.875rem",
    "4xl": "2.25rem",
    "5xl": "3rem",
    "6xl": "3.75rem",
    "7xl": "4.5rem",
    "8xl": "6rem",
    "9xl": "8rem",
  },
  lineHeight: "1.2em",
} as const;

/**
 * Spacing scale
 */
export const spacing = {
  0: "0",
  1: "0.25rem",
  2: "0.5rem",
  3: "0.75rem",
  4: "1rem",
  5: "1.25rem",
  6: "1.5rem",
  8: "2rem",
  10: "2.5rem",
  12: "3rem",
  16: "4rem",
  20: "5rem",
  24: "6rem",
  32: "8rem",
  40: "10rem",
  48: "12rem",
  56: "14rem",
  64: "16rem",
} as const;

/**
 * Breakpoints
 */
export const breakpoints = {
  mobile: { max: "809px" },
  tablet: { min: "810px", max: "1199px" },
  desktop: { min: "1200px" },
} as const;

/**
 * Animation configurations
 */
export const animations = {
  spring: {
    damping: 60,
    stiffness: 200,
    mass: 1,
  },
  delays: {
    1: 0.2,
    2: 0.5,
    3: 0.7,
    4: 1.0,
    5: 1.1,
    6: 1.2,
  },
  durations: {
    fast: 0.3,
    normal: 0.6,
    slow: 1.0,
  },
} as const;

/**
 * Component variants
 */
export const variants = {
  button: {
    primary: "bg-primary text-white hover:bg-primary-600 focus:ring-primary-300",
    secondary: "bg-white text-primary border border-primary hover:bg-primary-50",
    ghost: "text-primary hover:bg-primary-50",
  },
  text: {
    heading: "font-display font-bold",
    body: "font-body font-normal",
    caption: "font-body font-normal text-sm",
  },
} as const;

/**
 * Utility functions for responsive design
 */
export const responsive = {
  isMobile: (width: number) => width <= 809,
  isTablet: (width: number) => width >= 810 && width <= 1199,
  isDesktop: (width: number) => width >= 1200,
} as const;

/**
 * Common component styles
 */
export const commonStyles = {
  container: "mx-auto px-4 sm:px-6 lg:px-8",
  section: "py-16 lg:py-24",
  card: "bg-white rounded-lg shadow-sm border border-gray-10",
  button: "inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-smooth focus-visible",
  input: "block w-full rounded-md border border-gray-20 px-3 py-2 text-sm focus:border-primary focus:ring-primary",
} as const;
