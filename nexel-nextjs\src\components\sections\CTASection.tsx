"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import Container from "@/components/ui/Container";
import Section from "@/components/ui/Section";
import Button from "@/components/ui/Button";
import { cn } from "@/lib/design-system";
import type { CTASectionProps } from "@/types";

export default function CTASection({
  title,
  description,
  buttonText,
  buttonLink,
  secondaryButtonText,
  secondaryButtonLink,
  background = "primary",
  className,
}: CTASectionProps) {
  const isLightBackground = background === "white" || background === "gray";
  const textColor = isLightBackground ? "text-dark" : "text-white";
  const subtextColor = isLightBackground ? "text-gray" : "text-white-70";

  return (
    <Section background={background} className={className}>
      <Container>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center max-w-4xl mx-auto"
        >
          {/* Title */}
          <h2 className={cn(
            "text-3xl md:text-4xl lg:text-5xl font-display font-bold mb-6",
            textColor
          )}>
            {title}
          </h2>

          {/* Description */}
          {description && (
            <p className={cn(
              "text-lg md:text-xl leading-relaxed mb-8",
              subtextColor
            )}>
              {description}
            </p>
          )}

          {/* Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link href={buttonLink}>
              <Button
                variant={isLightBackground ? "primary" : "secondary"}
                size="lg"
                className="w-full sm:w-auto"
              >
                {buttonText}
              </Button>
            </Link>
            
            {secondaryButtonText && secondaryButtonLink && (
              <Link href={secondaryButtonLink}>
                <Button
                  variant="ghost"
                  size="lg"
                  className={cn(
                    "w-full sm:w-auto",
                    isLightBackground 
                      ? "text-primary hover:bg-primary-50" 
                      : "text-white hover:bg-white-10"
                  )}
                >
                  {secondaryButtonText}
                </Button>
              </Link>
            )}
          </motion.div>

          {/* Decorative elements for primary background */}
          {background === "primary" && (
            <div className="relative mt-16">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="absolute -top-8 -left-8 w-16 h-16 border-2 border-white-20 rounded-full"
              />
              <motion.div
                animate={{ rotate: -360 }}
                transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
                className="absolute -bottom-8 -right-8 w-12 h-12 border-2 border-white-15 rounded-full"
              />
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                className="absolute top-1/2 -right-12 w-6 h-6 bg-white-10 rounded-full"
              />
            </div>
          )}
        </motion.div>
      </Container>
    </Section>
  );
}
