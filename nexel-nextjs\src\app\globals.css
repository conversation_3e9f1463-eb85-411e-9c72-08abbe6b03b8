@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

:root {
  /* Brand Colors */
  --primary: rgb(79, 26, 214);
  --primary-50: rgba(79, 26, 214, 0.02);
  --primary-100: rgba(79, 26, 214, 0.05);
  --primary-200: rgba(79, 26, 214, 0.08);
  --primary-300: rgba(79, 26, 214, 0.15);
  --primary-400: rgba(79, 26, 214, 0.2);

  /* Neutral Colors */
  --white: rgb(255, 255, 255);
  --white-60: rgba(255, 255, 255, 0.6);
  --white-50: rgba(255, 255, 255, 0.5);
  --white-30: rgba(255, 255, 255, 0.3);
  --white-20: rgba(255, 255, 255, 0.2);
  --white-15: rgba(255, 255, 255, 0.15);
  --white-10: rgba(255, 255, 255, 0.1);
  --white-8: rgba(255, 255, 255, 0.08);
  --white-7: rgba(255, 255, 255, 0.07);
  --white-5: rgba(255, 255, 255, 0.05);
  --white-2: rgba(255, 255, 255, 0.02);

  --dark: rgb(0, 0, 0);
  --dark-light: rgb(8, 8, 8);
  --dark-medium: rgb(19, 19, 19);
  --dark-30: rgba(0, 0, 0, 0.3);

  --gray: rgb(153, 153, 153);
  --gray-light: rgb(239, 238, 236);
  --gray-10: rgba(153, 153, 153, 0.1);
  --gray-20: rgba(51, 51, 51, 0.2);

  /* Accent Colors */
  --accent-red: rgb(255, 0, 0);
  --accent-blue: rgb(0, 128, 255);

  /* Legacy support */
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

* {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'DM Sans', 'Inter', 'Inter Placeholder', sans-serif;
  font-size: 16px;
  line-height: 1.2em;
  margin: 0;
  padding: 0;
}

/* Animation utilities */
.animate-appear {
  opacity: 0.001;
  animation: appear 0.6s ease-out forwards;
}

.animate-appear-delay-1 {
  animation-delay: 0.2s;
}

.animate-appear-delay-2 {
  animation-delay: 0.5s;
}

.animate-appear-delay-3 {
  animation-delay: 0.7s;
}

.animate-appear-delay-4 {
  animation-delay: 1s;
}

.animate-appear-delay-5 {
  animation-delay: 1.1s;
}

.animate-appear-delay-6 {
  animation-delay: 1.2s;
}

/* Typography utilities */
.text-balance {
  text-wrap: balance;
}

/* Focus styles */
.focus-visible:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Smooth transitions */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
