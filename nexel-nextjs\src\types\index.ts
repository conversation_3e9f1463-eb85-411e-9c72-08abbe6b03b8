// Core application types

export interface NavItem {
  label: string;
  href: string;
  external?: boolean;
  children?: NavItem[];
}

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon?: string;
  image?: string;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  slug: string;
  category: string;
  featured?: boolean;
  technologies?: string[];
  client?: string;
  year?: number;
  url?: string;
}

export interface Testimonial {
  id: string;
  name: string;
  company: string;
  content: string;
  rating: number;
  avatar?: string;
  position?: string;
}

export interface ContactInfo {
  email: string;
  phone: string;
  address: string;
  hours: string;
  social?: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
}

export interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio: string;
  avatar: string;
  social?: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
}

// Component prop types
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

export interface ContainerProps {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export interface SectionProps {
  children: React.ReactNode;
  background?: 'white' | 'gray' | 'primary' | 'dark';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export interface CardProps {
  children: React.ReactNode;
  hover?: boolean;
  shadow?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface InputProps {
  type?: 'text' | 'email' | 'tel' | 'password' | 'textarea';
  label?: string;
  error?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  required?: boolean;
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

// Section component props
export interface HeroSectionProps {
  title: string;
  subtitle?: string;
  ctaText?: string;
  ctaLink?: string;
  secondaryCtaText?: string;
  secondaryCtaLink?: string;
  backgroundImage?: string;
  className?: string;
}

export interface FeaturesSectionProps {
  features: Feature[];
  title?: string;
  subtitle?: string;
  layout?: 'grid' | 'list';
  className?: string;
}

export interface PortfolioSectionProps {
  projects: Project[];
  title?: string;
  subtitle?: string;
  showAll?: boolean;
  limit?: number;
  className?: string;
}

export interface TestimonialsSectionProps {
  testimonials: Testimonial[];
  title?: string;
  subtitle?: string;
  autoplay?: boolean;
  className?: string;
}

export interface ContactSectionProps {
  showForm?: boolean;
  showMap?: boolean;
  contactInfo: ContactInfo;
  className?: string;
}

export interface CTASectionProps {
  title: string;
  description?: string;
  buttonText: string;
  buttonLink: string;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
  background?: 'white' | 'gray' | 'primary' | 'dark';
  className?: string;
}

// Animation types
export interface AnimationConfig {
  type?: 'spring' | 'tween';
  duration?: number;
  delay?: number;
  damping?: number;
  stiffness?: number;
  mass?: number;
}

export interface AnimatedElementProps {
  children: React.ReactNode;
  animation?: 'fadeIn' | 'fadeInUp' | 'scaleIn' | 'slideInLeft' | 'slideInRight';
  delay?: number;
  duration?: number;
  className?: string;
}

// Form types
export interface ContactFormData {
  name: string;
  email: string;
  company?: string;
  message: string;
}

export interface FormErrors {
  [key: string]: string;
}

// API types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// SEO types
export interface SEOProps {
  title: string;
  description: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  noindex?: boolean;
}

// Utility types
export type Variant = 'primary' | 'secondary' | 'ghost' | 'outline';
export type Size = 'sm' | 'md' | 'lg' | 'xl';
export type Color = 'primary' | 'secondary' | 'success' | 'warning' | 'error';
export type Background = 'white' | 'gray' | 'primary' | 'dark';

// Layout types
export interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

export interface HeaderProps {
  className?: string;
}

export interface FooterProps {
  className?: string;
}

export interface NavigationProps {
  items: NavItem[];
  className?: string;
}

export interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  items: NavItem[];
}

// Page types
export interface PageProps {
  params?: { [key: string]: string };
  searchParams?: { [key: string]: string | string[] | undefined };
}

export interface ProjectPageProps extends PageProps {
  params: { slug: string };
}

// Content types
export interface PageContent {
  title: string;
  description: string;
  content: string;
  slug: string;
  publishedAt: string;
  updatedAt: string;
}

export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  ogImage: string;
  links: {
    twitter?: string;
    github?: string;
    linkedin?: string;
  };
}
