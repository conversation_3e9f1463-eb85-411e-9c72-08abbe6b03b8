import type { Metadata } from "next";
import { DM_Sans, Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

const dmSans = DM_Sans({
  subsets: ["latin"],
  variable: "--font-dm-sans",
  display: "swap",
});

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Nexel - AI Engineer for PCB",
  description: "Nexel is the AI-powered platform that transforms your product ideas into ready-to-build PCBs. Design, simulate, and prototype hardware—faster than ever.",
  keywords: ["PCB design", "AI", "hardware", "electronics", "prototype", "simulation"],
  authors: [{ name: "Nexel" }],
  creator: "Nexel",
  publisher: "Nexel",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://nexel.ai",
    title: "N<PERSON>el - AI Engineer for PCB",
    description: "Nexel is the AI-powered platform that transforms your product ideas into ready-to-build PCBs. Design, simulate, and prototype hardware—faster than ever.",
    siteName: "Nexel",
  },
  twitter: {
    card: "summary_large_image",
    title: "Nexel - AI Engineer for PCB",
    description: "Nexel is the AI-powered platform that transforms your product ideas into ready-to-build PCBs. Design, simulate, and prototype hardware—faster than ever.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${dmSans.variable} ${inter.variable}`}>
      <body className="antialiased">
        <Header />
        <main className="pt-16">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
