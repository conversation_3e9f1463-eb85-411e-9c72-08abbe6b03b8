"use client";

import { motion } from "framer-motion";
import { Zap, Brain, Cpu, Layers } from "lucide-react";
import Container from "@/components/ui/Container";
import Section from "@/components/ui/Section";
import { cn } from "@/lib/design-system";
import type { FeaturesSectionProps, Feature } from "@/types";

const defaultFeatures: Feature[] = [
  {
    id: "ai-design",
    title: "AI-Powered Design",
    description: "Transform your ideas into professional PCB layouts using advanced AI algorithms that understand electronics design principles.",
    icon: "brain",
  },
  {
    id: "real-time-simulation",
    title: "Real-time Simulation",
    description: "Test and validate your designs instantly with our built-in simulation engine before moving to physical prototyping.",
    icon: "zap",
  },
  {
    id: "component-library",
    title: "Extensive Component Library",
    description: "Access thousands of verified components with real-time availability and pricing from trusted suppliers.",
    icon: "cpu",
  },
  {
    id: "collaborative-design",
    title: "Collaborative Design",
    description: "Work seamlessly with your team using real-time collaboration tools and version control for hardware projects.",
    icon: "layers",
  },
];

const iconMap = {
  brain: Brain,
  zap: Zap,
  cpu: Cpu,
  layers: Layers,
};

export default function FeaturesSection({
  features = defaultFeatures,
  title = "Revolutionize Your PCB Design Process",
  subtitle = "Nexel combines the power of AI with intuitive design tools to make hardware development faster, smarter, and more accessible than ever before.",
  layout = "grid",
  className,
}: FeaturesSectionProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <Section background="white" className={className}>
      <Container>
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold text-dark mb-6">
            {title}
          </h2>
          {subtitle && (
            <p className="text-lg md:text-xl text-gray max-w-3xl mx-auto leading-relaxed">
              {subtitle}
            </p>
          )}
        </motion.div>

        {/* Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className={cn(
            layout === "grid"
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
              : "space-y-8"
          )}
        >
          {features.map((feature, index) => {
            const IconComponent = iconMap[feature.icon as keyof typeof iconMap] || Brain;
            
            return (
              <motion.div
                key={feature.id}
                variants={itemVariants}
                className="group"
              >
                <div className="relative p-6 bg-white rounded-2xl border border-gray-10 hover:border-primary-200 transition-all duration-300 hover:shadow-lg">
                  {/* Icon */}
                  <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mb-4 group-hover:bg-primary-200 transition-colors duration-300">
                    <IconComponent className="w-6 h-6 text-primary" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-display font-semibold text-dark mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Hover effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl pointer-events-none" />
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-16"
        >
          <p className="text-gray mb-6">
            Ready to experience the future of PCB design?
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-primary text-white px-8 py-3 rounded-lg font-medium hover:bg-primary-600 transition-colors duration-200"
          >
            Start Your Free Trial
          </motion.button>
        </motion.div>
      </Container>
    </Section>
  );
}
