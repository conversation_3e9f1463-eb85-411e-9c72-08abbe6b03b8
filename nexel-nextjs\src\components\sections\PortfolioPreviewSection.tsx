"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import Container from "@/components/ui/Container";
import Section from "@/components/ui/Section";
import Button from "@/components/ui/Button";
import { cn } from "@/lib/design-system";
import type { Project } from "@/types";

const featuredProjects: Project[] = [
  {
    id: "lemonide-tech",
    title: "Lemonide Tech",
    description: "AI-powered IoT sensor platform for smart agriculture with real-time monitoring and predictive analytics.",
    image: "/portfolio/lemonide-tech.jpg",
    slug: "lemonide-tech",
    category: "IoT & Agriculture",
    featured: true,
    technologies: ["AI/ML", "IoT", "PCB Design"],
    client: "Lemonide Technologies",
    year: 2024,
  },
  {
    id: "million-one",
    title: "Million One",
    description: "High-performance computing board for edge AI applications with optimized power efficiency.",
    image: "/portfolio/million-one.jpg",
    slug: "million-one",
    category: "Edge Computing",
    featured: true,
    technologies: ["Edge AI", "High Performance", "Power Optimization"],
    client: "Million One Systems",
    year: 2024,
  },
  {
    id: "viper-studio",
    title: "Viper Studio",
    description: "Professional audio processing unit with custom DSP implementation and ultra-low latency design.",
    image: "/portfolio/viper-studio.jpg",
    slug: "viper-studio",
    category: "Audio Technology",
    featured: true,
    technologies: ["DSP", "Audio Processing", "Low Latency"],
    client: "Viper Studio",
    year: 2023,
  },
];

interface PortfolioPreviewSectionProps {
  title?: string;
  subtitle?: string;
  projects?: Project[];
  className?: string;
}

export default function PortfolioPreviewSection({
  title = "Transforming Ideas into Reality",
  subtitle = "See how we've helped innovative companies bring their hardware visions to life with our AI-powered PCB design platform.",
  projects = featuredProjects,
  className,
}: PortfolioPreviewSectionProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <Section background="gray" className={className}>
      <Container>
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-display font-bold text-dark mb-6">
            {title}
          </h2>
          {subtitle && (
            <p className="text-lg md:text-xl text-gray max-w-3xl mx-auto leading-relaxed">
              {subtitle}
            </p>
          )}
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12"
        >
          {projects.slice(0, 3).map((project, index) => (
            <motion.div
              key={project.id}
              variants={itemVariants}
              className="group"
            >
              <Link href={`/portfolio/${project.slug}`}>
                <div className="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300 group-hover:-translate-y-2">
                  {/* Project Image */}
                  <div className="relative aspect-video bg-gradient-to-br from-primary-100 to-primary-200 overflow-hidden">
                    {/* Placeholder for project image */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-xl">
                            {project.title.charAt(0)}
                          </span>
                        </div>
                        <p className="text-primary font-medium">{project.category}</p>
                      </div>
                    </div>
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-dark-30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <span className="text-white font-medium">View Case Study</span>
                    </div>
                  </div>

                  {/* Project Content */}
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-primary bg-primary-100 px-3 py-1 rounded-full">
                        {project.category}
                      </span>
                      <span className="text-sm text-gray">{project.year}</span>
                    </div>
                    
                    <h3 className="text-xl font-display font-semibold text-dark mb-3 group-hover:text-primary transition-colors duration-200">
                      {project.title}
                    </h3>
                    
                    <p className="text-gray leading-relaxed mb-4">
                      {project.description}
                    </p>

                    {/* Technologies */}
                    {project.technologies && (
                      <div className="flex flex-wrap gap-2">
                        {project.technologies.slice(0, 3).map((tech) => (
                          <span
                            key={tech}
                            className="text-xs text-gray bg-gray-10 px-2 py-1 rounded"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* View All CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <Link href="/portfolio">
            <Button variant="outline" size="lg">
              View All Projects
            </Button>
          </Link>
        </motion.div>
      </Container>
    </Section>
  );
}
