{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Zero%20Engineer/nexel-nextjs/src/lib/design-system.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\n\n/**\n * Utility function to merge class names\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * Design system color palette\n */\nexport const colors = {\n  primary: {\n    DEFAULT: \"rgb(79, 26, 214)\",\n    50: \"rgba(79, 26, 214, 0.02)\",\n    100: \"rgba(79, 26, 214, 0.05)\",\n    200: \"rgba(79, 26, 214, 0.08)\",\n    300: \"rgba(79, 26, 214, 0.15)\",\n    400: \"rgba(79, 26, 214, 0.2)\",\n    500: \"rgb(79, 26, 214)\",\n    600: \"rgb(63, 21, 171)\",\n    700: \"rgb(47, 16, 128)\",\n    800: \"rgb(32, 11, 86)\",\n    900: \"rgb(16, 5, 43)\",\n  },\n  white: {\n    DEFAULT: \"rgb(255, 255, 255)\",\n    90: \"rgba(255, 255, 255, 0.9)\",\n    80: \"rgba(255, 255, 255, 0.8)\",\n    70: \"rgba(255, 255, 255, 0.7)\",\n    60: \"rgba(255, 255, 255, 0.6)\",\n    50: \"rgba(255, 255, 255, 0.5)\",\n    30: \"rgba(255, 255, 255, 0.3)\",\n    20: \"rgba(255, 255, 255, 0.2)\",\n    15: \"rgba(255, 255, 255, 0.15)\",\n    10: \"rgba(255, 255, 255, 0.1)\",\n    8: \"rgba(255, 255, 255, 0.08)\",\n    7: \"rgba(255, 255, 255, 0.07)\",\n    5: \"rgba(255, 255, 255, 0.05)\",\n    2: \"rgba(255, 255, 255, 0.02)\",\n  },\n  dark: {\n    DEFAULT: \"rgb(0, 0, 0)\",\n    light: \"rgb(8, 8, 8)\",\n    medium: \"rgb(19, 19, 19)\",\n    30: \"rgba(0, 0, 0, 0.3)\",\n  },\n  gray: {\n    DEFAULT: \"rgb(153, 153, 153)\",\n    light: \"rgb(239, 238, 236)\",\n    10: \"rgba(153, 153, 153, 0.1)\",\n    20: \"rgba(51, 51, 51, 0.2)\",\n    90: \"rgba(97, 97, 97, 0.09)\",\n  },\n  accent: {\n    red: \"rgb(255, 0, 0)\",\n    blue: \"rgb(0, 128, 255)\",\n  },\n} as const;\n\n/**\n * Typography scale\n */\nexport const typography = {\n  fontFamily: {\n    sans: [\"DM Sans\", \"Inter\", \"Inter Placeholder\", \"sans-serif\"],\n    display: [\"DM Sans\", \"sans-serif\"],\n    body: [\"Inter\", \"Inter Placeholder\", \"sans-serif\"],\n  },\n  fontSize: {\n    xs: \"0.75rem\",\n    sm: \"0.875rem\",\n    base: \"1rem\",\n    lg: \"1.125rem\",\n    xl: \"1.25rem\",\n    \"2xl\": \"1.5rem\",\n    \"3xl\": \"1.875rem\",\n    \"4xl\": \"2.25rem\",\n    \"5xl\": \"3rem\",\n    \"6xl\": \"3.75rem\",\n    \"7xl\": \"4.5rem\",\n    \"8xl\": \"6rem\",\n    \"9xl\": \"8rem\",\n  },\n  lineHeight: \"1.2em\",\n} as const;\n\n/**\n * Spacing scale\n */\nexport const spacing = {\n  0: \"0\",\n  1: \"0.25rem\",\n  2: \"0.5rem\",\n  3: \"0.75rem\",\n  4: \"1rem\",\n  5: \"1.25rem\",\n  6: \"1.5rem\",\n  8: \"2rem\",\n  10: \"2.5rem\",\n  12: \"3rem\",\n  16: \"4rem\",\n  20: \"5rem\",\n  24: \"6rem\",\n  32: \"8rem\",\n  40: \"10rem\",\n  48: \"12rem\",\n  56: \"14rem\",\n  64: \"16rem\",\n} as const;\n\n/**\n * Breakpoints\n */\nexport const breakpoints = {\n  mobile: { max: \"809px\" },\n  tablet: { min: \"810px\", max: \"1199px\" },\n  desktop: { min: \"1200px\" },\n} as const;\n\n/**\n * Animation configurations\n */\nexport const animations = {\n  spring: {\n    damping: 60,\n    stiffness: 200,\n    mass: 1,\n  },\n  delays: {\n    1: 0.2,\n    2: 0.5,\n    3: 0.7,\n    4: 1.0,\n    5: 1.1,\n    6: 1.2,\n  },\n  durations: {\n    fast: 0.3,\n    normal: 0.6,\n    slow: 1.0,\n  },\n} as const;\n\n/**\n * Component variants\n */\nexport const variants = {\n  button: {\n    primary: \"bg-primary text-white hover:bg-primary-600 focus:ring-primary-300\",\n    secondary: \"bg-white text-primary border border-primary hover:bg-primary-50\",\n    ghost: \"text-primary hover:bg-primary-50\",\n  },\n  text: {\n    heading: \"font-display font-bold\",\n    body: \"font-body font-normal\",\n    caption: \"font-body font-normal text-sm\",\n  },\n} as const;\n\n/**\n * Utility functions for responsive design\n */\nexport const responsive = {\n  isMobile: (width: number) => width <= 809,\n  isTablet: (width: number) => width >= 810 && width <= 1199,\n  isDesktop: (width: number) => width >= 1200,\n} as const;\n\n/**\n * Common component styles\n */\nexport const commonStyles = {\n  container: \"mx-auto px-4 sm:px-6 lg:px-8\",\n  section: \"py-16 lg:py-24\",\n  card: \"bg-white rounded-lg shadow-sm border border-gray-10\",\n  button: \"inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-smooth focus-visible\",\n  input: \"block w-full rounded-md border border-gray-20 px-3 py-2 text-sm focus:border-primary focus:ring-primary\",\n} as const;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,KAAK;AACd;AAKO,MAAM,SAAS;IACpB,SAAS;QACP,SAAS;QACT,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,OAAO;QACL,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,MAAM;QACJ,SAAS;QACT,OAAO;QACP,QAAQ;QACR,IAAI;IACN;IACA,MAAM;QACJ,SAAS;QACT,OAAO;QACP,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IACA,QAAQ;QACN,KAAK;QACL,MAAM;IACR;AACF;AAKO,MAAM,aAAa;IACxB,YAAY;QACV,MAAM;YAAC;YAAW;YAAS;YAAqB;SAAa;QAC7D,SAAS;YAAC;YAAW;SAAa;QAClC,MAAM;YAAC;YAAS;YAAqB;SAAa;IACpD;IACA,UAAU;QACR,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,YAAY;AACd;AAKO,MAAM,UAAU;IACrB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAKO,MAAM,cAAc;IACzB,QAAQ;QAAE,KAAK;IAAQ;IACvB,QAAQ;QAAE,KAAK;QAAS,KAAK;IAAS;IACtC,SAAS;QAAE,KAAK;IAAS;AAC3B;AAKO,MAAM,aAAa;IACxB,QAAQ;QACN,SAAS;QACT,WAAW;QACX,MAAM;IACR;IACA,QAAQ;QACN,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,MAAM;IACR;AACF;AAKO,MAAM,WAAW;IACtB,QAAQ;QACN,SAAS;QACT,WAAW;QACX,OAAO;IACT;IACA,MAAM;QACJ,SAAS;QACT,MAAM;QACN,SAAS;IACX;AACF;AAKO,MAAM,aAAa;IACxB,UAAU,CAAC,QAAkB,SAAS;IACtC,UAAU,CAAC,QAAkB,SAAS,OAAO,SAAS;IACtD,WAAW,CAAC,QAAkB,SAAS;AACzC;AAKO,MAAM,eAAe;IAC1B,WAAW;IACX,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACT", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Zero%20Engineer/nexel-nextjs/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Menu, X } from \"lucide-react\";\nimport { cn } from \"@/lib/design-system\";\nimport type { NavItem, HeaderProps } from \"@/types\";\n\nconst navItems: NavItem[] = [\n  { label: \"Home\", href: \"/\" },\n  { label: \"About\", href: \"/about\" },\n  { label: \"Portfolio\", href: \"/portfolio\" },\n  { label: \"Contact\", href: \"/contact\" },\n];\n\nexport default function Header({ className }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  return (\n    <header className={cn(\"fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-10\", className)}>\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-primary rounded-md flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">N</span>\n            </div>\n            <span className=\"text-xl font-display font-bold text-dark\">Nexel</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-dark hover:text-primary transition-colors duration-200 font-medium\"\n              >\n                {item.label}\n              </Link>\n            ))}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-600 transition-colors duration-200 font-medium\"\n            >\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={toggleMobileMenu}\n            className=\"md:hidden p-2 text-dark hover:text-primary transition-colors duration-200\"\n            aria-label=\"Toggle mobile menu\"\n          >\n            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3, ease: \"easeInOut\" }}\n            className=\"md:hidden bg-white border-b border-gray-10\"\n          >\n            <div className=\"container mx-auto px-4 py-4\">\n              <nav className=\"flex flex-col space-y-4\">\n                {navItems.map((item) => (\n                  <Link\n                    key={item.href}\n                    href={item.href}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className=\"text-dark hover:text-primary transition-colors duration-200 font-medium py-2\"\n                  >\n                    {item.label}\n                  </Link>\n                ))}\n                <Link\n                  href=\"/contact\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className=\"bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-600 transition-colors duration-200 font-medium text-center mt-4\"\n                >\n                  Get Started\n                </Link>\n              </nav>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;;;;;;;;;AAGA;AANA;;;;;;;AASA,MAAM,WAAsB;IAC1B;QAAE,OAAO;QAAQ,MAAM;IAAI;IAC3B;QAAE,OAAO;QAAS,MAAM;IAAS;IACjC;QAAE,OAAO;QAAa,MAAM;IAAa;IACzC;QAAE,OAAO;QAAW,MAAM;IAAW;CACtC;AAEc,SAAS,OAAO,EAAE,SAAS,EAAe;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE,8OAAC;QAAO,WAAW,CAAA,GAAA,8HAAA,CAAA,KAAE,AAAD,EAAE,wFAAwF;;0BAC5G,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;sCAI7D,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,IAAI;;;;;;;;;;sCAUpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAW;sCAEV,iCAAmB,8OAAC;gCAAE,MAAM;;;;;qDAAS,8OAAC;gCAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAMxD,8OAAC;0BACE,kCACC,8OAAC,OAAO,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAET,KAAK,KAAK;uCALN,KAAK,IAAI;;;;;8CAQlB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,oBAAoB;oCACnC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}