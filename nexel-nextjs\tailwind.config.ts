import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Brand Colors
        primary: {
          DEFAULT: "rgb(79, 26, 214)",
          50: "rgba(79, 26, 214, 0.02)",
          100: "rgba(79, 26, 214, 0.05)",
          200: "rgba(79, 26, 214, 0.08)",
          300: "rgba(79, 26, 214, 0.15)",
          400: "rgba(79, 26, 214, 0.2)",
          500: "rgb(79, 26, 214)",
          600: "rgb(63, 21, 171)",
          700: "rgb(47, 16, 128)",
          800: "rgb(32, 11, 86)",
          900: "rgb(16, 5, 43)",
        },
        // Neutral Colors
        white: {
          DEFAULT: "rgb(255, 255, 255)",
          90: "rgba(255, 255, 255, 0.9)",
          80: "rgba(255, 255, 255, 0.8)",
          70: "rgba(255, 255, 255, 0.7)",
          60: "rgba(255, 255, 255, 0.6)",
          50: "rgba(255, 255, 255, 0.5)",
          30: "rgba(255, 255, 255, 0.3)",
          20: "rgba(255, 255, 255, 0.2)",
          15: "rgba(255, 255, 255, 0.15)",
          10: "rgba(255, 255, 255, 0.1)",
          8: "rgba(255, 255, 255, 0.08)",
          7: "rgba(255, 255, 255, 0.07)",
          5: "rgba(255, 255, 255, 0.05)",
          2: "rgba(255, 255, 255, 0.02)",
        },
        dark: {
          DEFAULT: "rgb(0, 0, 0)",
          light: "rgb(8, 8, 8)",
          medium: "rgb(19, 19, 19)",
          30: "rgba(0, 0, 0, 0.3)",
        },
        gray: {
          DEFAULT: "rgb(153, 153, 153)",
          light: "rgb(239, 238, 236)",
          10: "rgba(153, 153, 153, 0.1)",
          20: "rgba(51, 51, 51, 0.2)",
          90: "rgba(97, 97, 97, 0.09)",
        },
        // Accent Colors
        accent: {
          red: "rgb(255, 0, 0)",
          blue: "rgb(0, 128, 255)",
        },
        // Legacy support
        background: "var(--background)",
        foreground: "var(--foreground)",
      },
      fontFamily: {
        sans: ["DM Sans", "Inter", "Inter Placeholder", "sans-serif"],
        display: ["DM Sans", "sans-serif"],
        body: ["Inter", "Inter Placeholder", "sans-serif"],
      },
      fontSize: {
        xs: ["0.75rem", { lineHeight: "1.2em" }],
        sm: ["0.875rem", { lineHeight: "1.2em" }],
        base: ["1rem", { lineHeight: "1.2em" }],
        lg: ["1.125rem", { lineHeight: "1.2em" }],
        xl: ["1.25rem", { lineHeight: "1.2em" }],
        "2xl": ["1.5rem", { lineHeight: "1.2em" }],
        "3xl": ["1.875rem", { lineHeight: "1.2em" }],
        "4xl": ["2.25rem", { lineHeight: "1.2em" }],
        "5xl": ["3rem", { lineHeight: "1.2em" }],
        "6xl": ["3.75rem", { lineHeight: "1.2em" }],
        "7xl": ["4.5rem", { lineHeight: "1.2em" }],
        "8xl": ["6rem", { lineHeight: "1.2em" }],
        "9xl": ["8rem", { lineHeight: "1.2em" }],
      },
      screens: {
        mobile: { max: "809px" },
        tablet: { min: "810px", max: "1199px" },
        desktop: { min: "1200px" },
      },
      animation: {
        "fade-in": "fadeIn 0.6s ease-out forwards",
        "fade-in-up": "fadeInUp 0.6s ease-out forwards",
        "scale-in": "scaleIn 0.6s ease-out forwards",
        "appear-1": "appear 0.6s ease-out 0.2s forwards",
        "appear-2": "appear 0.6s ease-out 0.5s forwards",
        "appear-3": "appear 0.6s ease-out 0.7s forwards",
        "appear-4": "appear 0.6s ease-out 1s forwards",
        "appear-5": "appear 0.6s ease-out 1.1s forwards",
        "appear-6": "appear 0.6s ease-out 1.2s forwards",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0.001" },
          "100%": { opacity: "1" },
        },
        fadeInUp: {
          "0%": { opacity: "0.001", transform: "translateY(20px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        scaleIn: {
          "0%": { opacity: "0.001", transform: "scale(1.1)" },
          "100%": { opacity: "1", transform: "scale(1)" },
        },
        appear: {
          "0%": { opacity: "0.001" },
          "100%": { opacity: "1" },
        },
      },
    },
  },
  plugins: [],
};
export default config;
