# Component Architecture Plan

## Overview
This document outlines the React component structure for the Nexel Next.js application, based on the original Framer site analysis.

## Component Hierarchy

### 1. Layout Components (`src/components/layout/`)

#### `Header.tsx`
- **Purpose**: Main navigation header
- **Features**:
  - Logo/brand
  - Navigation menu (Home, About, Portfolio, Contact)
  - Mobile hamburger menu
  - Responsive design
- **Props**: `{ className?: string }`

#### `Footer.tsx`
- **Purpose**: Site footer
- **Features**:
  - Company information
  - Links to legal pages
  - Social media links
  - Copyright notice
- **Props**: `{ className?: string }`

#### `Navigation.tsx`
- **Purpose**: Reusable navigation component
- **Features**:
  - Active link highlighting
  - Smooth transitions
  - Mobile-friendly
- **Props**: `{ items: NavItem[], className?: string }`

#### `MobileMenu.tsx`
- **Purpose**: Mobile navigation overlay
- **Features**:
  - Slide-in animation
  - Full-screen overlay
  - Close button
- **Props**: `{ isOpen: boolean, onClose: () => void, items: NavItem[] }`

### 2. UI Components (`src/components/ui/`)

#### `Button.tsx`
- **Purpose**: Reusable button component
- **Variants**: primary, secondary, ghost, outline
- **Sizes**: sm, md, lg
- **Props**: `{ variant, size, children, onClick, disabled, className, ...rest }`

#### `Container.tsx`
- **Purpose**: Responsive container wrapper
- **Features**:
  - Max-width constraints
  - Responsive padding
  - Center alignment
- **Props**: `{ children, size?: 'sm' | 'md' | 'lg' | 'xl', className? }`

#### `Section.tsx`
- **Purpose**: Page section wrapper
- **Features**:
  - Consistent spacing
  - Background options
  - Animation triggers
- **Props**: `{ children, background?, padding?, className? }`

#### `Card.tsx`
- **Purpose**: Content card component
- **Features**:
  - Hover effects
  - Shadow variations
  - Rounded corners
- **Props**: `{ children, hover?, shadow?, className? }`

#### `Badge.tsx`
- **Purpose**: Small status/category indicators
- **Variants**: primary, secondary, success, warning, error
- **Props**: `{ children, variant, size?, className? }`

#### `Input.tsx`
- **Purpose**: Form input component
- **Types**: text, email, textarea
- **Features**:
  - Validation states
  - Label integration
  - Error messages
- **Props**: `{ type, label?, error?, placeholder?, ...rest }`

#### `Modal.tsx`
- **Purpose**: Modal/dialog component
- **Features**:
  - Backdrop blur
  - Escape key handling
  - Focus management
- **Props**: `{ isOpen, onClose, title?, children, size? }`

### 3. Section Components (`src/components/sections/`)

#### `HeroSection.tsx`
- **Purpose**: Main landing page hero
- **Features**:
  - Large heading with animation
  - Subtitle/description
  - CTA buttons
  - Background graphics
- **Props**: `{ title, subtitle, ctaText, ctaLink, backgroundImage? }`

#### `FeaturesSection.tsx`
- **Purpose**: Product features showcase
- **Features**:
  - Grid layout
  - Icon + text combinations
  - Staggered animations
- **Props**: `{ features: Feature[], title?, subtitle? }`

#### `PortfolioSection.tsx`
- **Purpose**: Portfolio/case studies preview
- **Features**:
  - Project cards
  - Hover effects
  - "View All" link
- **Props**: `{ projects: Project[], title?, showAll? }`

#### `TestimonialsSection.tsx`
- **Purpose**: Client testimonials
- **Features**:
  - Carousel/slider
  - Star ratings
  - Client photos
- **Props**: `{ testimonials: Testimonial[], autoplay? }`

#### `ContactSection.tsx`
- **Purpose**: Contact form and information
- **Features**:
  - Contact form
  - Company details
  - Map integration (optional)
- **Props**: `{ showForm?, showMap?, contactInfo }`

#### `CTASection.tsx`
- **Purpose**: Call-to-action sections
- **Features**:
  - Compelling copy
  - Action buttons
  - Background variations
- **Props**: `{ title, description, buttonText, buttonLink, background? }`

### 4. Page Components (`src/app/`)

#### `page.tsx` (Home)
- **Sections**:
  - HeroSection
  - FeaturesSection
  - PortfolioSection (preview)
  - TestimonialsSection
  - CTASection

#### `about/page.tsx`
- **Sections**:
  - About hero
  - Team section
  - Company story
  - Values/mission

#### `portfolio/page.tsx`
- **Sections**:
  - Portfolio hero
  - Project grid
  - Case study previews
  - Process overview

#### `portfolio/[slug]/page.tsx`
- **Sections**:
  - Project hero
  - Project details
  - Results/metrics
  - Related projects

#### `contact/page.tsx`
- **Sections**:
  - Contact hero
  - Contact form
  - Office information
  - FAQ section

### 5. Utility Components

#### `AnimatedElement.tsx`
- **Purpose**: Wrapper for Framer Motion animations
- **Features**:
  - Intersection observer
  - Staggered children
  - Custom animations
- **Props**: `{ children, animation?, delay?, className? }`

#### `Image.tsx`
- **Purpose**: Optimized image component
- **Features**:
  - Next.js Image optimization
  - Lazy loading
  - Placeholder handling
- **Props**: `{ src, alt, width?, height?, priority?, className? }`

#### `Link.tsx`
- **Purpose**: Enhanced link component
- **Features**:
  - Next.js Link integration
  - External link handling
  - Active state styling
- **Props**: `{ href, children, external?, className? }`

## Data Types

### Core Types (`src/types/`)

```typescript
interface NavItem {
  label: string;
  href: string;
  external?: boolean;
}

interface Feature {
  id: string;
  title: string;
  description: string;
  icon?: string;
}

interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  slug: string;
  category: string;
  featured?: boolean;
}

interface Testimonial {
  id: string;
  name: string;
  company: string;
  content: string;
  rating: number;
  avatar?: string;
}

interface ContactInfo {
  email: string;
  phone: string;
  address: string;
  hours: string;
}
```

## Animation Strategy

### Framer Motion Integration
- **Page transitions**: Smooth page-to-page animations
- **Scroll animations**: Elements animate in on scroll
- **Hover effects**: Interactive component states
- **Staggered animations**: Sequential element reveals

### Animation Patterns
1. **Fade in**: Opacity 0 → 1
2. **Slide up**: Transform translateY(20px) → 0
3. **Scale in**: Transform scale(0.95) → 1
4. **Stagger**: Sequential delays for child elements

## Responsive Strategy

### Breakpoints
- **Mobile**: ≤ 809px
- **Tablet**: 810px - 1199px
- **Desktop**: ≥ 1200px

### Component Adaptations
- **Header**: Hamburger menu on mobile
- **Sections**: Stack vertically on mobile
- **Grid layouts**: Responsive column counts
- **Typography**: Scaled font sizes

## Performance Considerations

### Code Splitting
- **Page-level**: Automatic with Next.js App Router
- **Component-level**: Dynamic imports for heavy components
- **Third-party**: Lazy load external libraries

### Image Optimization
- **Next.js Image**: Automatic optimization
- **WebP format**: Modern image formats
- **Lazy loading**: Below-the-fold images

### Bundle Optimization
- **Tree shaking**: Remove unused code
- **CSS purging**: Remove unused styles
- **Compression**: Gzip/Brotli compression

## Development Guidelines

### Component Standards
1. **TypeScript**: All components use TypeScript
2. **Props interface**: Define clear prop types
3. **Default props**: Provide sensible defaults
4. **Error boundaries**: Handle component errors
5. **Accessibility**: ARIA labels and keyboard navigation

### File Organization
```
src/
├── components/
│   ├── ui/           # Reusable UI components
│   ├── layout/       # Layout components
│   └── sections/     # Page sections
├── lib/              # Utilities and helpers
├── types/            # TypeScript type definitions
└── app/              # Next.js pages
```

### Naming Conventions
- **Components**: PascalCase (e.g., `HeroSection.tsx`)
- **Props**: camelCase interfaces (e.g., `HeroSectionProps`)
- **Files**: kebab-case for non-components (e.g., `design-system.ts`)
- **CSS classes**: Tailwind utilities + custom classes
